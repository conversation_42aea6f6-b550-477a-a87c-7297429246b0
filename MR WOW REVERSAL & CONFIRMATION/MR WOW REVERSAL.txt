// This source code is subject to the terms of the Copyright Private License - All rights reserved.
// Stochastic index with price channel, custom levels and buy/sell signals.
// Updated: 1.0 / 2024-05-15
// ©revetrading


// ********************************************************************************************************************
// CONFIGURACION INDICADOR
// ********************************************************************************************************************
//@version=5
indicator("MW WOW REVERSAL", shorttitle = "MR WOW REVERSAL",
  overlay = false,
  precision = 2
  // timeframe = "",
  // timeframe_gaps = false
  )


// ********************************************************************************************************************
// CONSTANTES Y HELPERS
// ********************************************************************************************************************

// Colores
StoCol = color.purple
LvlCol = color.blue
ChnCol = color.silver
BuyCol = color.green
SelCol = color.red
TxtCol = color.black

// Helpers
plotDis = display.all - display.price_scale
plotVal = 0.0
ND = display.none


// ********************************************************************************************************************
// INPUTS
// ********************************************************************************************************************

// Grupo base
prcSrcIn = input.string("close", "Price source ", group="Stoch config",
             options=["close", "open", "hl2", "hlc3", "ohlc4"] )

stoLenIn = input.int(48, "Stoch length", minval=1, step=1, group="Stoch config")

smoLenIn = input.int(0, "Smooth length", minval=0, step=1, group="Stoch config")

smoTypIn = input.string("SMA", "Smooth type", group="Stoch config",
             options=["SMA", "EMA", "WMA", "RMA", "HMA", "VWMA", "ALMA", "LREG"] )

// Grupo niveles
lvlUpIn  = input.int(80, "Up level", minval=50, maxval=100, step=1, display=ND, group="Levels config")
lvlDnIn  = input.int(20, "Down level", minval=0, maxval=50, step=1, display=ND, group="Levels config")
lvlMdIn  = input.int(50, "Mid level", minval=50, maxval=50, step=1, display=ND, group="Levels config")

// Grupo señales
buyLvlIn  = input.string("DN", "Buy level", group="Signals config",
              options=["UP", "MD", "DN"] )

selLvlIn  = input.string("UP", "Sell level", group="Signals config",
              options=["UP", "MD", "DN"] )

chnMinRIn = input.float(0, "Min wide (%)", minval=0, maxval=50, step=0.1, group="Signals config")


// ********************************************************************************************************************
// FUNCIONES
// ********************************************************************************************************************

getSrc(srcIn) =>
    // Devuelve float de string source
    src = switch srcIn
        "close" => close
        "open"  => open
        "hl2"   => hl2
        "hlc3"  => hlc3
        "ohlc4" => ohlc4
        => close
    // Retorno
    src
// /function


getStoData(src, len) =>
    // *** Devuelve índice stoch y precios min/max
    // Precios min/max y diferencia
    prcMin = ta.lowest(low,len)
    prcMax = ta.highest(high,len)
    prcDif = src - prcMin
    // A índice stoch 0-100
    stoIndex = 100 * prcDif / (prcMax - prcMin)
    // Retorna tupla
    [stoIndex, prcMin, prcMax]
// /function


getMaVal(src, len, typ) =>
    // Devuelve float media móvil
    float maVal = na
    switch typ
        "EMA"  => maVal := ta.ema(src, len)
        "SMA"  => maVal := ta.sma(src, len)
        "WMA"  => maVal := ta.wma(src, len)
        "RMA"  => maVal := ta.rma(src, len)
        "HMA"  => maVal := ta.hma(src, len)
        "VWMA" => maVal := ta.vwma(src, len)
        "ALMA" => maVal := ta.alma(src, len, 0.85, 6)
        "LREG" => maVal := ta.linreg(src, len, 0)
    // Retorno    
    maVal
//  /function


// ********************************************************************************************************************
// CALCULOS
// ********************************************************************************************************************

// Fuente precio, string a float
prcSrc = getSrc(prcSrcIn)

// Indice stoch y precios max/min
[stoIndex, prcMin, prcMax] = getStoData(prcSrc, stoLenIn)

// Suavizado stoch opcional
stoIndex := smoLenIn <= 1 ? stoIndex : getMaVal(stoIndex, smoLenIn, smoTypIn) 

// Niveles precios bajo/medio/alto
prcDn = prcMin + (prcMax - prcMin) * lvlDnIn/100
prcMd = prcMin + (prcMax - prcMin) * lvlMdIn/100
prcUp = prcMin + (prcMax - prcMin) * lvlUpIn/100


// Porcentaje ancho canal: diferencia niveles up/dn
chnWideR = 100 * (prcUp - prcDn) / prcDn
chnIsWide = chnWideR >= chnMinRIn ? true : false


// Compra: nivel señal
buyLvl = switch buyLvlIn
    "UP" => lvlUpIn
    "DN" => lvlDnIn
    => lvlMdIn

// Compra: cruce arriba + mínimo canal
buySignal = ta.crossover(stoIndex, buyLvl) and chnIsWide ? true : false


// Venta: nivel señal
selLvl = switch selLvlIn
    "UP" => lvlUpIn
    "DN" => lvlDnIn
    => lvlMdIn

// Venta: cruce abajo + mínimo canal
selSignal = ta.crossunder(stoIndex, selLvl) and chnIsWide ? true : false


// Conector/señal externa: 0/1/-1
extSignal = buySignal==true ? 1 : (selSignal==true ? -1 : 0)


// ********************************************************************************************************************
// OUTPUTS
// ********************************************************************************************************************

// PLOTS EN AUXILIAR

// Niveles canal min/max, 0-100
hline(0, "Min level", ChnCol, hline.style_dashed, 1)
hline(100, "Max level", ChnCol, hline.style_dashed, 1)

// Niveles stoch up/md/dn
lvlDnHl = hline(lvlDnIn, "Down level", LvlCol, hline.style_dashed, 1)
lvlMdHl = hline(lvlMdIn, "Mid level", LvlCol, hline.style_dashed, 1)
lvlUpHl = hline(lvlUpIn, "Up level", LvlCol, hline.style_dashed, 1)
fill(lvlDnHl, lvlUpHl, color.new(LvlCol,97), "Stoch fill")

// Indice stoch
plotDis := display.all - display.price_scale
plot(stoIndex, "Stoch index", StoCol, 1, plot.style_line, display=plotDis, force_overlay=false)

// Señales compra/venta
plotDis := display.pane

plotVal := buySignal==true ? stoIndex : na
plot(plotVal, "Buy signal", BuyCol, 3, plot.style_circles, display=plotDis, force_overlay=false)

plotVal := selSignal==true ? stoIndex : na
plot(plotVal, "Sell signal", SelCol, 3, plot.style_circles, display=plotDis, force_overlay=false)


// PLOTS EN PRINCIPAL

// Línea de precios
plotDis := display.pane
plot(prcSrc, "Source price", StoCol, 1, plot.style_stepline, display=plotDis, force_overlay=true)

// Bandas precios min/max
plotDis := display.pane
prcMinPl = plot(prcMin, "Min price", ChnCol, 1, plot.style_line, display=plotDis, force_overlay=true)
prcMaxPl = plot(prcMax, "Max price", ChnCol, 1, plot.style_line, display=plotDis, force_overlay=true)

// Bandas precios down/mid/up
prcDnPl = plot(prcDn, "Down price", color.new(LvlCol,50), 1, plot.style_line, display=plotDis, force_overlay=true)
prcMdPl = plot(prcMd, "Mid price", color.new(LvlCol,0), 1, plot.style_line, display=plotDis, force_overlay=true)
prcUpPl = plot(prcUp, "Up price", color.new(LvlCol,50), 1, plot.style_line, display=plotDis, force_overlay=true)

fill(prcDnPl, prcUpPl, color.new(LvlCol, 97), "Price fill")

// Señales compra/venta
plotDis := display.pane

plotchar(buySignal, "Buy signal", "▲", location.belowbar, BuyCol, text="Buy", display=plotDis, force_overlay=true)
plotchar(selSignal, "Sell signal", "▼", location.abovebar, SelCol, text="Sell", display=plotDis, force_overlay=true)


// PLOTS EN DATA WINDOW/DEPURACION
// Ancho canal
plotDis := display.data_window
plot(chnWideR, "Channel wide (%)", TxtCol, display=plotDis, editable=false, force_overlay=false)

// Señal externa
plot(extSignal, "STOCHN signal", TxtCol, display=plotDis, editable=false, force_overlay=false)


// ETIQUETAS
if barstate.islast
    var lbChnWide = label(na)
    lbChnWide.delete()
    lbChnWide := label.new(
      x         = bar_index + 3,
      y         = lvlDnIn,
      text      = "Wide: " + str.tostring(chnWideR, "#0.00") + "%",
      color     = color.new(LvlCol, 30),
      textcolor = color.white,
      style     = label.style_label_left,
      size      = size.small
      )


// EOF: ©revetrading - All rights reserved 