//@version=5

indicator("MaYF FX+Price Action", "MaYFFX+Price Action", overlay=true, max_labels_count=500, max_lines_count=500, max_boxes_count=500, max_bars_back=500)

max_bars_back = 1200
max_labels_count = 1200
max_lines_count = 1200
//-----------------------------------------------------------------------------{
    //Boolean set
//-----------------------------------------------------------------------------{
s_BOS        = 0
s_CHoCH      = 1
i_BOS        = 2
i_CHoCH      = 3
i_pp_CHoCH   = 4
green_candle = 5
red_candle   = 6
s_CHoCHP     = 7
i_CHoCHP     = 8

boolean = array.from(false, false, false, false, false, false, false, false, false)

//-----------------------------------------------------------------------------{
    // User inputs
//-----------------------------------------------------------------------------{
show_swing_ms                   = input.string      ("All"                            , "Swing        "               , inline = "1", group = "MARKET STRUCTURE"            , options = ["All", "CHoCH", "CHoCH+", "BOS", "None"])
show_internal_ms                = input.string      ("All"                            , "Internal     "               , inline = "2", group = "MARKET STRUCTURE"            , options = ["All", "CHoCH", "CHoCH+", "BOS", "None"])
internal_r_lookback             = input.int         (4                                , ""                            , inline = "2", group = "MARKET STRUCTURE"            , minval = 2)
swing_r_lookback                = input.int         (50                               , ""                            , inline = "1", group = "MARKET STRUCTURE"            , minval = 2)
ms_mode                         = input.string      ("Manual"                         , "Market Structure Mode"       , inline = "a", group = "MARKET STRUCTURE"            , tooltip = "[Manual] Use selected lenght\n[Dynamic] Use automatic lenght" ,options = ["Manual", "Dynamic"])

show_mtf_str                    = input.bool        (true                             , "MTF Scanner"                 , inline = "9", group = "MARKET STRUCTURE"            , tooltip = "Display Multi-Timeframe Market Structure Trend Directions. Green = Bullish. Red = Bearish")

show_itrend5 = input(true, title="Show 5m")
show_itrend15 = input(true, title="Show 15m")
show_itrend30 = input(true, title="Show 30m")
show_itrend1H = input(true, title="Show 1H")
show_itrend4H = input(true, title="Show 4H")
show_itrend1D = input(true, title="Show 1D")
show_itrend1W = input(true, title="Show 1W")
show_rsi = input(true, title="Show RSI")
show_adx = input(true, title="Show ADX")
lookback = 499

show_sfp = input.bool(false, "Show Swing Failure Patern", group="MARKET STRUCTURE")
pvllen = input.int(25, "Pivot Length", 1, 99, group="MARKET STRUCTURE")
show_eql                        = input.bool        (false                            , "Show EQH/EQL"                , inline = "6", group = "MARKET STRUCTURE")
plotcandle_bool                 = input.bool        (false                            , "Plotcandle"                  , inline = "3", group = "MARKET STRUCTURE"            , tooltip = "Displays a cleaner colored candlestick chart in place of the default candles. (requires hiding the current ticker candles)")
barcolor_bool                   = input.bool        (false                            , "Bar Color"                   , inline = "4", group = "MARKET STRUCTURE"            , tooltip = "Color the candle bodies according to market strucutre trend")

i_ms_up_BOS                   = input.color       (#089981                          , ""                            , inline = "2", group = "MARKET STRUCTURE")
i_ms_dn_BOS                   = input.color       (#f23645                          , ""                            , inline = "2", group = "MARKET STRUCTURE")
s_ms_up_BOS                   = input.color       (#089981                          , ""                            , inline = "1", group = "MARKET STRUCTURE")
s_ms_dn_BOS                   = input.color       (#f23645                          , ""                            , inline = "1", group = "MARKET STRUCTURE")

lvl_daily                       = input.bool        (false                            , "Day   "                      , inline = "1", group = "HIGHS & LOWS MTF")
lvl_weekly                      = input.bool        (false                            , "Week "                       , inline = "2", group = "HIGHS & LOWS MTF")
lvl_monthly                     = input.bool        (false                            , "Month"                       , inline = "3", group = "HIGHS & LOWS MTF")
lvl_yearly                      = input.bool        (false                            , "Year  "                      , inline = "4", group = "HIGHS & LOWS MTF")
css_d                           = input.color       (color.blue                     , ""                            , inline = "1", group = "HIGHS & LOWS MTF")
css_w                           = input.color       (color.blue                     , ""                            , inline = "2", group = "HIGHS & LOWS MTF")
css_m                           = input.color       (color.blue                     , ""                            , inline = "3", group = "HIGHS & LOWS MTF")
css_y                           = input.color       (color.blue                     , ""                            , inline = "4", group = "HIGHS & LOWS MTF")
s_d                             = input.string      ('⎯⎯⎯'                            , ''                            , inline = '1', group = 'HIGHS & LOWS MTF'                , options = ['⎯⎯⎯', '----', '····'])
s_w                             = input.string      ('⎯⎯⎯'                            , ''                            , inline = '2', group = 'HIGHS & LOWS MTF'                , options = ['⎯⎯⎯', '----', '····'])
s_m                             = input.string      ('⎯⎯⎯'                            , ''                            , inline = '3', group = 'HIGHS & LOWS MTF'                , options = ['⎯⎯⎯', '----', '····'])
s_y                             = input.string      ('⎯⎯⎯'                            , ''                            , inline = '4', group = 'HIGHS & LOWS MTF'                , options = ['⎯⎯⎯', '----', '····'])

ob_show                         = input.bool        (true                             , "Show Last    "               , inline = "1", group = "VOLUMETRIC ORDER BLOCKS"         , tooltip = "Display volumetric order blocks on the chart \n\n[Input] Ammount of volumetric order blocks to show")
ob_num                          = input.int         (5                                , ""                            , inline = "1", group = "VOLUMETRIC ORDER BLOCKS"         , tooltip = "Orderblocks number", minval = 1, maxval = 10)
ob_tf       = input.timeframe("","Timeframe", inline = "b", group = "VOLUMETRIC ORDER BLOCKS", tooltip="Enter Timeframe for Order Blocks ")
ob_metrics_show                 = input.bool        (true                             , "Internal Buy/Sell Activity"  , inline = "2", group = "VOLUMETRIC ORDER BLOCKS"         , tooltip = "Display volume metrics that have formed the orderblock")
css_metric_up                   = input.color       (color.new(#089981,  40)        , "         "                   , inline = "2", group = "VOLUMETRIC ORDER BLOCKS")
css_metric_dn                   = input.color       (color.new(#f23645 , 40)        , ""                            , inline = "2", group = "VOLUMETRIC ORDER BLOCKS")

ob_filter                       = input.string      ("None"                           , "Filtering             "      , inline = "d", group = "VOLUMETRIC ORDER BLOCKS"         , tooltip = "Filter out volumetric order blocks by BOS/CHoCH/CHoCH+", options = ["None", "BOS", "CHoCH", "CHoCH+"])
ob_mitigation                   = input.string      ("Absolute"                       , "Mitigation           "       , inline = "4", group = "VOLUMETRIC ORDER BLOCKS"         , tooltip = "Trigger to remove volumetric order blocks", options = ["Absolute", "Middle"])
ob_pos                          = input.string      ("Precise"                        , "Positioning          "       , inline = "k", group = "VOLUMETRIC ORDER BLOCKS"         , tooltip = "Position of the Order Block\n[Full] Cover the whole candle\n[Middle] Cover half candle\n[Accurate] Adjust to volatility\n[Precise] Same as Accurate but more precise", options = ["Full", "Middle", "Accurate", "Precise"])
use_grayscale                   = input.bool        (false                            , "Grayscale"                   , inline = "6", group = "VOLUMETRIC ORDER BLOCKS"         , tooltip = "Use gray as basic order blocks color")
use_show_metric                 = input.bool        (true                             , "Show Metrics"                , inline = "7", group = "VOLUMETRIC ORDER BLOCKS"         , tooltip = "Show volume associated with the orderblock and his relevance")
obtxt                           = input.string("Normal"            , "Metric Size"                    , ["Tiny", "Small", "Normal", "Large", "Huge"], inline = "8", group = "VOLUMETRIC ORDER BLOCKS" )
use_middle_line                 = input.bool        (true                             , "Show Middle-Line"            , inline = "9", group = "VOLUMETRIC ORDER BLOCKS"         , tooltip = "Show mid-line order blocks")

use_overlap                     = input.bool        (true                             , "Hide Overlap"                , inline = "10", group = "VOLUMETRIC ORDER BLOCKS"         , tooltip = "Hide overlapping order blocks")
use_overlap_method              = input.string      ("Previous"                       , "Overlap Method    "          , inline = "Z", group = "VOLUMETRIC ORDER BLOCKS"         , tooltip = "[Recent] Preserve the most recent volumetric order blocks\n\n[Previous] Preserve the previous volumetric order blocks", options = ["Recent", "Previous"])
ob_bull_css                     = input.color       (color.new(#089981 ,  80)       , ""                            , inline = "1", group = "VOLUMETRIC ORDER BLOCKS")
ob_bear_css                     = input.color       (color.new(#f23645 ,  80)       , ""                            , inline = "1", group = "VOLUMETRIC ORDER BLOCKS")

show_acc_dist_zone              = input.bool        (false                            , ""                            , inline = "1", group = "Accumulation And Distribution")
zone_mode                       = input.string      ("Fast"                           , ""                            , inline = "1", group = "Accumulation And Distribution"   , tooltip = "[Fast] Find small zone pattern formation\n[Slow] Find bigger zone pattern formation" ,options = ["Slow", "Fast"])
acc_css                         = input.color       (color.new(#089981   , 60)      , ""                            , inline = "1", group = "Accumulation And Distribution")
dist_css                        = input.color       (color.new(#f23645   , 60)      , ""                            , inline = "1", group = "Accumulation And Distribution")

show_lbl                        = input.bool        (true                            , "Show swing point"            , inline = "1", group = "High and Low"                    , tooltip = "Display swing point")
show_mtb                        = input.bool        (true                            , "Show High/Low/Equilibrium"   , inline = "2", group = "High and Low"                    , tooltip = "Display Strong/Weak High And Low and Equilibrium")
toplvl                          = input.color       (color.rgb(255, 0, 0)                      , "Premium Zone   "             , inline = "3", group = "High and Low")
midlvl                          = input.color       (color.gray                    , "Equilibrium Zone"            , inline = "4", group = "High and Low")
btmlvl                          = input.color       (#089981                        , "Discount Zone    "           , inline = "5", group = "High and Low")

fvg_enable                      = input.bool        (false                            , "        "                            , inline = "1", group = "FAIR VALUE GAP"          , tooltip = "Display fair value gap")
what_fvg                        = input.string      ("FVG"                            , ""                            , inline = "1", group = "FAIR VALUE GAP"                  , tooltip = "Display fair value gap", options = ["FVG", "VI", "OG"])
fvg_num                         = input.int         (5                                , "Show Last  "                   , inline = "1a", group = "FAIR VALUE GAP"               , tooltip = "Number of fvg to show")
fvg_upcss                       = input.color       (color.new(#089981,  80)        , ""                            , inline = "1", group = "FAIR VALUE GAP")
fvg_dncss                       = input.color       (color.new(color.red ,  80)     , ""                            , inline = "1", group = "FAIR VALUE GAP")
fvg_extend                      = input.int         (10                               , "Extend FVG"                  , inline = "2", group = "FAIR VALUE GAP"                  , tooltip = "Extend the display of the FVG.")
fvg_src                         = input.string      ("Close"                          , "Mitigation  "                , inline = "3", group = "FAIR VALUE GAP"                  , tooltip = "[Close] Use the close of the body as trigger\n\n[Wick] Use the extreme point of the body as trigger", options = ["Close", "Wick"])
fvg_tf                          = input.timeframe   (""                               , "Timeframe "                  , inline = "4", group = "FAIR VALUE GAP"                  , tooltip = "Timeframe of the fair value gap")

// Alert Settings Group
enable_alerts                   = input.bool        (true                             , "Enable All Alerts"           , group = "ALERT SETTINGS"                              , tooltip = "Master switch for all alerts")
alert_bos                       = input.bool        (true                             , "BOS Alerts"                  , inline = "a1", group = "ALERT SETTINGS"               , tooltip = "Break of Structure alerts")
alert_choch                     = input.bool        (true                             , "CHoCH Alerts"                , inline = "a2", group = "ALERT SETTINGS"               , tooltip = "Change of Character alerts")
alert_chochplus                 = input.bool        (true                             , "CHoCH+ Alerts"               , inline = "a3", group = "ALERT SETTINGS"               , tooltip = "Enhanced Change of Character alerts")
alert_swing_bos                 = input.bool        (true                             , "Swing BOS Alerts"            , inline = "a4", group = "ALERT SETTINGS"               , tooltip = "Swing Break of Structure alerts")
alert_swing_choch               = input.bool        (true                             , "Swing CHoCH Alerts"          , inline = "a5", group = "ALERT SETTINGS"               , tooltip = "Swing Change of Character alerts")
alert_swing_chochplus           = input.bool        (true                             , "Swing CHoCH+ Alerts"         , inline = "a6", group = "ALERT SETTINGS"               , tooltip = "Swing Enhanced Change of Character alerts")
alert_orderblocks               = input.bool        (true                             , "Order Block Alerts"          , inline = "a7", group = "ALERT SETTINGS"               , tooltip = "Order Block formation alerts")
alert_orderblock_touch          = input.bool        (true                             , "Order Block Touch Alerts"    , inline = "a8", group = "ALERT SETTINGS"               , tooltip = "Order Block touch/mitigation alerts")
alert_fvg                       = input.bool        (true                             , "Fair Value Gap Alerts"       , inline = "a9", group = "ALERT SETTINGS"               , tooltip = "Fair Value Gap formation alerts")
alert_equal_levels              = input.bool        (true                             , "Equal Highs/Lows Alerts"     , inline = "a10", group = "ALERT SETTINGS"              , tooltip = "Equal Highs and Equal Lows alerts")
alert_zones                     = input.bool        (true                             , "Accumulation/Distribution"    , inline = "a11", group = "ALERT SETTINGS"              , tooltip = "Accumulation and Distribution zone alerts")

rsi_enable                          = input.bool        (true                             , "Enable RSI"                                     , group = "RSI"                                         , tooltip = "Plot overbought and oversold price to bars")
rsi_src                             = close
rsi_overbought_src                  = close
rsi_oversold_src                    = close
rsi_length                          = input.int         (14, minval=1                      , title="Length"                                  , group = "RSI")
rsi_overbought_length               = input.int         (80, minval=1                      , title="Overbought               "               , inline="overbought"                                    , group = "RSI")
rsi_oversold_length                 = input.int         (20, minval=1                      , title="Oversold                   "             , inline = "oversold"                                    , group = "RSI")
rsi_overbought_color                = input.color       (#ff0000                     , "", inline = "overbought"                       , group = "RSI")
rsi_oversold_color                  = input.color       (#089981                      , "", inline = "oversold"                         , group = "RSI")

t                               = color.t           (ob_bull_css)
invcol                          = color.new         (color.white                    , 100)

fixnan(x) =>
    na(x) ? 0 : x

method txSz(string s) =>
    out = switch s
        "Tiny"   => size.tiny
        "Small"  => size.small
        "Normal" => size.normal
        "Large"  => size.large
        "Huge"   => size.huge
    out

type bar
    float   o = open
    float   c = close
    float   h = high
    float   l = low
    float   v = volume
    int     n = bar_index
    int     t = time

type Zphl
    line   top
    line   bottom
    label  top_label
    label  bottom_label
    bool   stopcross
    bool   sbottomcross
    bool   itopcross
    bool   ibottomcross
    string txtup
    string txtdn
    float  topy
    float  bottomy
    float  topx
    float  bottomx
    float  tup
    float  tdn
    int    tupx
    int    tdnx
    float  itopy
    float  itopx
    float  ibottomy
    float  ibottomx
    float  uV
    float  dV

type FVG
    box [] box
    line[] ln
    bool   bull
    float  top
    float  btm
    int    left
    int    right

type ms
	float[] p
	int  [] n
    float[] l

type msDraw
	int    n
	float  p
	color  css
	string txt
	bool   bull

type obC
    float[] top
    float[] btm
    int  [] left
    float[] avg
    float[] dV
    float[] cV
    int  [] wM
    int  [] blVP
    int  [] brVP
    int  [] dir
    float[] h
    float[] l
    int  [] n

type obD
    box [] ob
    box [] eOB
    box [] blB
    box [] brB
    line[] mL

type zone
    chart.point points
    float p
    int   c
    int   t

type hqlzone
    box   pbx
    box   ebx
    box   lbx
    label plb
    label elb
    label lbl

type ehl
    float pt
    int   t
    float pb
    int   b

type pattern
    string found = "None"
    bool isfound = false
    int   period = 0
    bool  bull   = false

type alerts
    bool chochswing     = false
    bool chochplusswing = false
    bool swingbos       = false
    bool chochplus      = false
    bool choch          = false
    bool bos            = false
    bool equal          = false
    bool ob             = false
    bool swingob        = false
    bool zone           = false
    bool fvg            = false
    bool obtouch        = false

bar         b      = bar.new()
var pattern p      = pattern.new()

alerts      blalert = alerts.new()
alerts      bralert = alerts.new()

if p.isfound
    p.period += 1

if p.period == 50
    p.period  := 0
    p.found   := "None"
    p.isfound := false
    p.bull    := na

switch
    b.c > b.o => boolean.set(green_candle, true)
    b.c < b.o => boolean.set(red_candle  , true)

f_zscore(src, lookback) =>
    (src - ta.sma(src, lookback)) / ta.stdev(src, lookback)

var int iLen = internal_r_lookback
var int sLen = swing_r_lookback

vv = f_zscore(((close - close[iLen]) / close[iLen]) * 100,iLen)

if ms_mode == "Dynamic"
    switch
        vv >= 1.5 or vv <= -1.5 => iLen := 10
        vv >= 1.6 or vv <= -1.6 => iLen := 9
        vv >= 1.7 or vv <= -1.7 => iLen := 8
        vv >= 1.8 or vv <= -1.8 => iLen := 7
        vv >= 1.9 or vv <= -1.9 => iLen := 6
        vv >= 2.0 or vv <= -2.0 => iLen := 5
        =>                         iLen

var msline = array.new<line>(0)

iH = ta.pivothigh(high, iLen, iLen)
sH = ta.pivothigh(high, sLen, sLen)
iL = ta.pivotlow (low , iLen, iLen)
sL = ta.pivotlow (low , sLen, sLen)

hl  () => [high, low]

[pdh, pdl] = request.security(syminfo.tickerid , 'D'  , hl() , lookahead = barmerge.lookahead_on)
[pwh, pwl] = request.security(syminfo.tickerid , 'W'  , hl() , lookahead = barmerge.lookahead_on)
[pmh, pml] = request.security(syminfo.tickerid , 'M'  , hl() , lookahead = barmerge.lookahead_on)
[pyh, pyl] = request.security(syminfo.tickerid , '12M', hl() , lookahead = barmerge.lookahead_on)

lstyle(style) =>
    out = switch style
        '⎯⎯⎯'  => line.style_solid
        '----' => line.style_dashed
        '····' => line.style_dotted

mtfphl(h, l ,tf ,css, pdhl_style) =>
    var line hl = line.new(
       na
     , na
     , na
     , na
     , xloc      = xloc.bar_time
     , color     = css
     , style     = lstyle(pdhl_style)
     )

    var line ll   = line.new(
       na
     , na
     , na
     , na
     , xloc      = xloc.bar_time
     , color     = css
     , style     = lstyle(pdhl_style)
     )

    var label lbl = label.new(
       na
     , na
     , xloc      = xloc.bar_time
     , text      = str.format('P{0}L', tf)
     , color     = invcol
     , textcolor = css
     , size      = size.small
     , style     = label.style_label_left
     )

    var label hlb = label.new(
       na
     , na
     , xloc      = xloc.bar_time
     , text      = str.format('P{0}H', tf)
     , color     = invcol
     , textcolor = css
     , size      = size.small
     , style     = label.style_label_left
     )

    hy = ta.valuewhen(h != h[1] , h    , 1)
    hx = ta.valuewhen(h == high , time , 1)
    ly = ta.valuewhen(l != l[1] , l    , 1)
    lx = ta.valuewhen(l == low  , time , 1)

    if barstate.islast
        extension = time + (time - time[1]) * 50
        line.set_xy1(hl , hx        , hy)
        line.set_xy2(hl , extension , hy)
        label.set_xy(hlb, extension , hy)
        line.set_xy1(ll , lx        , ly)
        line.set_xy2(ll , extension , ly)
        label.set_xy(lbl, extension , ly)

if lvl_daily
    mtfphl(pdh   , pdl , 'D'  , css_d, s_d)

if lvl_weekly
    mtfphl(pwh   , pwl , 'W'  , css_w, s_w)

if lvl_monthly
    mtfphl(pmh   , pml,  'M'  , css_m, s_m)

if lvl_yearly
    mtfphl(pyh   , pyl , '12M', css_y, s_y)

//{----------------------------------------------------------------------------------------------------------------------------------------------}
//{ - Market Structure                                                                                                                           }
//{----------------------------------------------------------------------------------------------------------------------------------------------}
method darkcss(color css, float factor, bool bull) =>
    blue  = color.b(css) * (1 - factor)
    red   = color.r(css) * (1 - factor)
    green = color.g(css) * (1 - factor)
    color.rgb(red, green, blue, 0)

method f_line(msDraw d, size, style) =>
    var line  id  = na
    var label lbl = na

    id := line.new(
       d.n
     , d.p
     , b.n
     , d.p
     , color = d.css
     , width = 1
     , style = style
     )

    if msline.size() >= 250
        line.delete(msline.shift())

    msline.push(id)

    lbl := label.new(
       int(math.avg(d.n, b.n))
     , d.p
     , d.txt
     , color            = invcol
     , textcolor        = d.css
     , style            = d.bull ? label.style_label_down : label.style_label_up
     , size             = size
     )

structure(bool mtf) =>
	msDraw drw     = na
    bool isdrw     = false
    bool isdrwS   = false
    var color css  = na
    var color icss = na
	var int itrend = 0
    var int  trend = 0
    bool bull_ob   = false
    bool bear_ob   = false
    bool s_bull_ob = false
    bool s_bear_ob = false
    n = bar_index

	var ms up = ms.new(
		   array.new<float>()
		 , array.new< int >()
         , array.new<float>()
		 )

	var ms dn = ms.new(
		   array.new<float>()
		 , array.new< int >()
         , array.new<float>()
		 )

	var ms sup = ms.new(
		   array.new<float>()
		 , array.new< int >()
         , array.new<float>()
		 )

	var ms sdn = ms.new(
		   array.new<float>()
		 , array.new< int >()
         , array.new<float>()
		 )

    switch show_swing_ms
        "All"      =>  boolean.set(s_BOS , true ),  boolean.set(s_CHoCH, true ) , boolean.set(s_CHoCHP, true  )
        "CHoCH"    =>  boolean.set(s_BOS , false),  boolean.set(s_CHoCH, true ) , boolean.set(s_CHoCHP, false )
        "CHoCH+"   =>  boolean.set(s_BOS , false),  boolean.set(s_CHoCH, false) , boolean.set(s_CHoCHP, true  )
        "BOS"      =>  boolean.set(s_BOS , true ),  boolean.set(s_CHoCH, false) , boolean.set(s_CHoCHP, false )
        "None"     =>  boolean.set(s_BOS , false),  boolean.set(s_CHoCH, false) , boolean.set(s_CHoCHP, false )
        => na

    switch show_internal_ms
        "All"      =>  boolean.set(i_BOS, true ),  boolean.set(i_CHoCH, true  ),  boolean.set(i_CHoCHP, true )
        "CHoCH"    =>  boolean.set(i_BOS, false),  boolean.set(i_CHoCH, true  ),  boolean.set(i_CHoCHP, false)
        "CHoCH+"   =>  boolean.set(i_BOS, false),  boolean.set(i_CHoCH, false ),  boolean.set(i_CHoCHP, true )
        "BOS"      =>  boolean.set(i_BOS, true ),  boolean.set(i_CHoCH, false ),  boolean.set(i_CHoCHP, false)
        "None"     =>  boolean.set(i_BOS, false),  boolean.set(i_CHoCH, false ),  boolean.set(i_CHoCHP, false)
        => na

    switch
        iH =>
            up.p.unshift(b.h[iLen])
            up.l.unshift(b.h[iLen])
            up.n.unshift(n  [iLen])

        iL =>
            dn.p.unshift(b.l[iLen])
            dn.l.unshift(b.l[iLen])
            dn.n.unshift(n  [iLen])

        sL =>
            sdn.p.unshift(b.l[sLen])
            sdn.l.unshift(b.l[sLen])
            sdn.n.unshift(n  [sLen])

        sH =>
            sup.p.unshift(b.h[sLen])
            sup.l.unshift(b.h[sLen])
            sup.n.unshift(n  [sLen])

	// INTERNAL BULLISH STRUCTURE
	if up.p.size() > 0 and dn.l.size() > 1
		if ta.crossover(b.c, up.p.first())
			bool CHoCH = na
			string txt = na

			if itrend < 0
				CHoCH := true

			switch
				not CHoCH =>
					txt := "BOS"
					css := i_ms_up_BOS
                    blalert.bos := true

					if boolean.get(i_BOS) and mtf == false and na(drw)
                        isdrw := true
						drw := msDraw.new(
							   up.n.first()
							 , up.p.first()
							 , i_ms_up_BOS
							 , txt
							 , true
							 )

				CHoCH =>
                    if dn.l.first() > dn.l.get(1)
                        blalert.chochplus := true
                    else
                        blalert.choch := true

					txt := dn.l.first() > dn.l.get(1) ? "CHoCH+" : "CHoCH"
					css := i_ms_up_BOS.darkcss(0.25, true)

					if (dn.l.first() > dn.l.get(1) ? boolean.get(i_CHoCHP) : boolean.get(i_CHoCH)) and mtf == false and na(drw)
                        isdrw := true
						drw := msDraw.new(
							   up.n.first()
							 , up.p.first()
							 , i_ms_up_BOS.darkcss(0.25, true)
							 , txt
							 , true
							 )

			if mtf == false
				switch
					ob_filter == "None" 					    => bull_ob := true
					ob_filter == "BOS"    and txt == "BOS"      => bull_ob := true
					ob_filter == "CHoCH"  and txt == "CHoCH"    => bull_ob := true
					ob_filter == "CHoCH+" and txt == "CHoCH+"   => bull_ob := true

			itrend := 1
            up.n.clear()
            up.p.clear()

	// INTERNAL BEARISH STRUCTURE
	if dn.p.size() > 0 and up.l.size() > 1
		if ta.crossunder(b.c, dn.p.first())
			bool CHoCH = na
			string txt = na

			if itrend > 0
				CHoCH := true

			switch
				not CHoCH =>
                    bralert.bos := true
					txt := "BOS"
					css := i_ms_dn_BOS

					if boolean.get(i_BOS) and mtf == false and na(drw)
                        isdrw := true
						drw := msDraw.new(
							   dn.n.first()
							 , dn.p.first()
							 , i_ms_dn_BOS
							 , txt
							 , false
							 )

				CHoCH =>
                    if up.l.first() < up.l.get(1)
                        bralert.chochplus := true
                    else
                        bralert.choch := true

					txt := up.l.first() < up.l.get(1) ? "CHoCH+" : "CHoCH"
					css := i_ms_dn_BOS.darkcss(0.25, false)

					if (up.l.first() < up.l.get(1) ? boolean.get(i_CHoCHP) : boolean.get(i_CHoCH)) and mtf == false and na(drw)
                        isdrw := true
						drw := msDraw.new(
							   dn.n.first()
							 , dn.p.first()
							 , i_ms_dn_BOS.darkcss(0.25, false)
							 , txt
							 , false
							 )

			if mtf == false
				switch
					ob_filter == "None" 					    => bear_ob := true
					ob_filter == "BOS"    and txt == "BOS"      => bear_ob := true
					ob_filter == "CHoCH"  and txt == "CHoCH"    => bear_ob := true
					ob_filter == "CHoCH+" and txt == "CHoCH+"   => bear_ob := true

			itrend := -1
            dn.n.clear()
            dn.p.clear()

	// SWING BULLISH STRUCTURE
	if sup.p.size() > 0 and sdn.l.size() > 1
		if ta.crossover(b.c, sup.p.first())
			bool CHoCH = na
			string txt = na

			if trend < 0
				CHoCH := true

			switch
				not CHoCH =>
                    blalert.swingbos := true
					txt := "BOS"
					icss := s_ms_up_BOS

					if boolean.get(s_BOS) and mtf == false and na(drw)
                        isdrwS := true
						drw := msDraw.new(
							   sup.n.first()
							 , sup.p.first()
							 , s_ms_up_BOS
							 , txt
							 , true
							 )

				CHoCH =>
                    if sdn.l.first() > sdn.l.get(1)
                        blalert.chochplusswing := true
                    else
                        blalert.chochswing := true

					txt := sdn.l.first() > sdn.l.get(1) ? "CHoCH+" : "CHoCH"
					icss := s_ms_up_BOS.darkcss(0.25, true)

					if (sdn.l.first() > sdn.l.get(1) ? boolean.get(s_CHoCHP) : boolean.get(s_CHoCH)) and mtf == false and na(drw)
                        isdrwS := true
						drw := msDraw.new(
							   sup.n.first()
							 , sup.p.first()
							 , s_ms_up_BOS.darkcss(0.25, true)
							 , txt
							 , true
							 )

			if mtf == false
				switch
					ob_filter == "None" 					  => s_bull_ob := true
					ob_filter == "BOS"    and txt == "BOS"    => s_bull_ob := true
					ob_filter == "CHoCH"  and txt == "CHoCH"  => s_bull_ob := true
					ob_filter == "CHoCH+" and txt == "CHoCH+" => s_bull_ob := true

			trend := 1
            sup.n.clear()
            sup.p.clear()

	// SWING BEARISH STRUCTURE
	if sdn.p.size() > 0 and sup.l.size() > 1
		if ta.crossunder(b.c, sdn.p.first())
			bool CHoCH = na
			string txt = na

			if trend > 0
				CHoCH := true

			switch
				not CHoCH =>
                    bralert.swingbos := true
					txt := "BOS"
					icss := s_ms_dn_BOS

					if boolean.get(s_BOS) and mtf == false and na(drw)
                        isdrwS := true
						drw := msDraw.new(
							   sdn.n.first()
							 , sdn.p.first()
							 , s_ms_dn_BOS
							 , txt
							 , false
							 )

				CHoCH =>
                    if sup.l.first() < sup.l.get(1)
                        bralert.chochplusswing := true
                    else
                        bralert.chochswing := true

					txt := sup.l.first() < sup.l.get(1) ? "CHoCH+" : "CHoCH"
					icss := s_ms_dn_BOS.darkcss(0.25, false)

					if (sup.l.first() < sup.l.get(1) ? boolean.get(s_CHoCHP) : boolean.get(s_CHoCH)) and mtf == false and na(drw)
                        isdrwS := true
						drw := msDraw.new(
							   sdn.n.first()
							 , sdn.p.first()
							 , s_ms_dn_BOS.darkcss(0.25, false)
							 , txt
							 , false
							 )

			if mtf == false
				switch
					ob_filter == "None" 					   => s_bear_ob := true
					ob_filter == "BOS"     and txt == "BOS"    => s_bear_ob := true
					ob_filter == "CHoCH"   and txt == "CHoCH"  => s_bear_ob := true
					ob_filter == "CHoCH+"  and txt == "CHoCH+" => s_bear_ob := true

			trend := -1
            sdn.n.clear()
            sdn.p.clear()

    [css, bear_ob, bull_ob, itrend, drw, isdrw, s_bear_ob, s_bull_ob, trend, icss, isdrwS]

[css, bear_ob, bull_ob, itrend, drw, isdrw, s_bear_ob, s_bull_ob, trend, icss, isdrwS] = structure(false)

if isdrw
    f_line(drw, size.small, line.style_dashed)

if isdrwS
    f_line(drw, size.small, line.style_solid)

// ADX Function
adxlen = input(14, title="ADX Smoothing")
dilen = input(14, title="DI Length")

dirmov(len) =>
    up = ta.change(high)
    down = -ta.change(low)
    plusDM = na(up) ? na : (up > down and up > 0 ? up : 0)
    minusDM = na(down) ? na : (down > up and down > 0 ? down : 0)
    truerange = ta.rma(ta.tr, len)
    plus = fixnan(100 * ta.rma(plusDM, len) / truerange)
    minus = fixnan(100 * ta.rma(minusDM, len) / truerange)
    [plus, minus]

adx(dilen, adxlen) =>
    [plus, minus] = dirmov(dilen)
    sum = plus + minus
    adx = 100 * ta.rma(math.abs(plus - minus) / (sum == 0 ? 1 : sum), adxlen)
    adx

//{----------------------------------------------------------------------------------------------------------------------------------------------}
//{ - RSI                                                                                                                                        }
//{----------------------------------------------------------------------------------------------------------------------------------------------}
up = ta.rma(math.max(ta.change(rsi_src), 0), rsi_length)
down = ta.rma(-math.min(ta.change(rsi_src), 0), rsi_length)
rsi = down == 0 ? 100 : up == 0 ? 0 : 100 - (100 / (1 + up / down))

isoverbought() => rsi > rsi_overbought_length
isoversold() => rsi < rsi_oversold_length

p_css = css
b_css = css
w_css = css

p_css := plotcandle_bool ? (css) : na
b_css := barcolor_bool   ? (css) : na
w_css := plotcandle_bool ? color.rgb(120, 123, 134, 50)           : na

plotcandle(open,high,low,close , color = p_css , wickcolor = w_css , bordercolor = p_css , editable = false)
barcolor(b_css, editable = false)

// Multi-Timeframe Analysis
[_, _, _, itrend5, _, _, _, _, _, _, _]  = request.security("", "5"      , structure(true))
[_, _, _, itrend15, _, _, _, _, _, _, _] = request.security("", "15"     , structure(true))
[_, _, _, itrend30, _, _, _, _, _, _, _] = request.security("", "30"     , structure(true))
[_, _, _, itrend1H, _, _, _, _, _, _, _] = request.security("", "60"     , structure(true))
[_, _, _, itrend4H, _, _, _, _, _, _, _] = request.security("", "240"    , structure(true))
[_, _, _, itrend1D, _, _, _, _, _, _, _] = request.security("", "1440"   , structure(true))
[_, _, _, itrend1W, _, _, _, _, _, _, _] = request.security("", "1W"     , structure(true))

rsi_value = ta.rsi(close, rsi_length)
rsi_value_str = str.format("{0,number,#.##}", rsi_value)

var tab = table.new(position = position.top_right, columns = 20, rows = 20, bgcolor = #000000, frame_color = #000000, border_width = 1)

// Table headers
if show_itrend5 and show_mtf_str
    table.cell(tab, 0, 1, text = show_itrend5 ? "5m" : "", text_color = color.silver, text_halign = text.align_center, text_size = size.normal, bgcolor = chart.bg_color, text_font_family = font.family_monospace, width = 4)
if show_itrend15 and show_mtf_str
    table.cell(tab, 0, 2, text = show_itrend15 ? "15m" : "", text_color = color.silver, text_halign = text.align_center, text_size = size.normal, bgcolor = chart.bg_color, text_font_family = font.family_monospace, width = 4)
if show_itrend30 and show_mtf_str
    table.cell(tab, 0, 3, text = show_itrend30 ? "30m" : "", text_color = color.silver, text_halign = text.align_center, text_size = size.normal, bgcolor = chart.bg_color, text_font_family = font.family_monospace, width = 4)
if show_itrend1H and show_mtf_str
    table.cell(tab, 0, 4, text = show_itrend1H ? "1H" : "", text_color = color.silver, text_halign = text.align_center, text_size = size.normal, bgcolor = chart.bg_color, text_font_family = font.family_monospace, width = 4)
if show_itrend4H and show_mtf_str
    table.cell(tab, 0, 5, text = show_itrend4H ? "4H" : "", text_color = color.silver, text_halign = text.align_center, text_size = size.normal, bgcolor = chart.bg_color, text_font_family = font.family_monospace, width = 4)
if show_itrend1D and show_mtf_str
    table.cell(tab, 0, 6, text = show_itrend1D ? "1D" : "", text_color = color.silver, text_halign = text.align_center, text_size = size.normal, bgcolor = chart.bg_color, text_font_family = font.family_monospace, width = 4)
if show_itrend1W and show_mtf_str
    table.cell(tab, 0, 7, text = show_itrend1W ? "1W" : "", text_color = color.silver, text_halign = text.align_center, text_size = size.normal, bgcolor = chart.bg_color, text_font_family = font.family_monospace, width = 4)
if show_rsi and show_mtf_str
    table.cell(tab, 0, 8, text = show_rsi ? "RSI" : "", text_color = color.yellow, text_halign = text.align_center, text_size = size.normal, bgcolor = chart.bg_color, text_font_family = font.family_monospace, width = 4)

    // Table data based on user preferences
if show_itrend5 and show_mtf_str
    table.cell(tab, 1, 1, text = itrend5 == 1 ? "BULLISH" : itrend5 == -1 ? "BEARISH" : na, text_halign = text.align_center, text_size = size.normal, text_color = itrend5 == 1 ? #089981 : itrend5 == -1 ? #ff0000 : color.gray, bgcolor = chart.bg_color, text_font_family = font.family_monospace)
if show_itrend15 and show_mtf_str
    table.cell(tab, 1, 2, text = itrend15 == 1 ? "BULLISH" : itrend15 == -1 ? "BEARISH" : na, text_halign = text.align_center, text_size = size.normal, text_color = itrend15 == 1 ? #089981 : itrend15 == -1 ? #ff0000 : color.gray, bgcolor = chart.bg_color, text_font_family = font.family_monospace)
if show_itrend30 and show_mtf_str
    table.cell(tab, 1, 3, text = itrend30 == 1 ? "BULLISH" : itrend30 == -1 ? "BEARISH" : na, text_halign = text.align_center, text_size = size.normal, text_color = itrend30 == 1 ? #089981 : itrend30 == -1 ? #ff0000 : color.gray, bgcolor = chart.bg_color, text_font_family = font.family_monospace)
if show_itrend1H and show_mtf_str
    table.cell(tab, 1, 4, text = itrend1H == 1 ? "BULLISH" : itrend1H == -1 ? "BEARISH" : na, text_halign = text.align_center, text_size = size.normal, text_color = itrend1H == 1 ? #089981 : itrend1H == -1 ? #ff0000 : color.gray, bgcolor = chart.bg_color, text_font_family = font.family_monospace)
if show_itrend4H and show_mtf_str
    table.cell(tab, 1, 5, text = itrend4H == 1 ? "BULLISH" : itrend4H == -1 ? "BEARISH" : na, text_halign = text.align_center, text_size = size.normal, text_color = itrend4H == 1 ? #089981 : itrend4H == -1 ? #ff0000 : color.gray, bgcolor = chart.bg_color, text_font_family = font.family_monospace)
if show_itrend1D and show_mtf_str
    table.cell(tab, 1, 6, text = itrend1D == 1 ? "BULLISH" : itrend1D == -1 ? "BEARISH" : na, text_halign = text.align_center, text_size = size.normal, text_color = itrend1D == 1 ? #089981 : itrend1D == -1 ? #ff0000 : color.gray, bgcolor = chart.bg_color, text_font_family = font.family_monospace)
if show_itrend1W and show_mtf_str
    table.cell(tab, 1, 7, text = itrend1W == 1 ? "BULLISH" : itrend1W == -1 ? "BEARISH" : na, text_halign = text.align_center, text_size = size.normal, text_color = itrend1W == 1 ? #089981 : itrend1W == -1 ? #ff0000 : color.gray, bgcolor = chart.bg_color, text_font_family = font.family_monospace)
if show_rsi and show_mtf_str
    table.cell(tab, 1, 8, text = rsi_value_str, text_halign = text.align_center, text_size = size.normal, text_color = isoverbought() ? #ff0000 : isoversold() ? #089981 : color.yellow, bgcolor = chart.bg_color, text_font_family = font.family_monospace)
// ADX data
if show_adx and show_mtf_str
    adx_value = adx(dilen, adxlen)
    table.cell(tab, 0, 9, text="ADX", text_halign=text.align_center, text_size=size.normal, text_color=#00e2ff, bgcolor=chart.bg_color, text_font_family=font.family_monospace, width=4)
    table.cell(tab, 1, 9, text=str.tostring(adx_value, "#.##"), text_halign=text.align_center, text_size=size.normal, text_color=#00e2ff, bgcolor=chart.bg_color, text_font_family=font.family_monospace)
    // Merged cells for pattern detection
if show_mtf_str
    table.cell(tab, 0, 10, text = "Detected Pattern", text_halign = text.align_center, text_size = size.normal, text_color = color.silver, bgcolor = chart.bg_color, text_font_family = font.family_monospace)
    table.cell(tab, 0, 11, text = p.found, text_halign = text.align_center, text_size = size.normal, text_color = na(p.bull) ? color.white : p.bull ? #089981 : #ff0000, bgcolor = chart.bg_color, text_font_family = font.family_monospace)

    table.merge_cells(tab, 0, 10, 1, 10)
    table.merge_cells(tab, 0, 11, 1, 11)

//{----------------------------------------------------------------------------------------------------------------------------------------------}
//{ - COMPREHENSIVE ALERT SYSTEM                                                                                                                 }
//{----------------------------------------------------------------------------------------------------------------------------------------------}

// Internal Market Structure Alerts
alertcondition(enable_alerts and alert_bos and blalert.bos,
    title="🟢 Bullish BOS (Internal)",
    message="🟢 BULLISH BREAK OF STRUCTURE (Internal) detected on {{ticker}} {{interval}}\n📈 Price broke above previous high\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_bos and bralert.bos,
    title="🔴 Bearish BOS (Internal)",
    message="🔴 BEARISH BREAK OF STRUCTURE (Internal) detected on {{ticker}} {{interval}}\n📉 Price broke below previous low\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_choch and blalert.choch,
    title="🟢 Bullish CHoCH (Internal)",
    message="🟢 BULLISH CHANGE OF CHARACTER (Internal) detected on {{ticker}} {{interval}}\n🔄 Market structure shifted bullish\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_choch and bralert.choch,
    title="🔴 Bearish CHoCH (Internal)",
    message="🔴 BEARISH CHANGE OF CHARACTER (Internal) detected on {{ticker}} {{interval}}\n🔄 Market structure shifted bearish\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_chochplus and blalert.chochplus,
    title="🟢 Bullish CHoCH+ (Internal)",
    message="🟢 BULLISH ENHANCED CHANGE OF CHARACTER (Internal) detected on {{ticker}} {{interval}}\n🚀 Strong bullish structure shift\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_chochplus and bralert.chochplus,
    title="🔴 Bearish CHoCH+ (Internal)",
    message="🔴 BEARISH ENHANCED CHANGE OF CHARACTER (Internal) detected on {{ticker}} {{interval}}\n💥 Strong bearish structure shift\n⏰ Time: {{time}}")

// Swing Market Structure Alerts
alertcondition(enable_alerts and alert_swing_bos and blalert.swingbos,
    title="🟢 Bullish BOS (Swing)",
    message="🟢 BULLISH BREAK OF STRUCTURE (Swing) detected on {{ticker}} {{interval}}\n📈 Major swing high broken\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_swing_bos and bralert.swingbos,
    title="🔴 Bearish BOS (Swing)",
    message="🔴 BEARISH BREAK OF STRUCTURE (Swing) detected on {{ticker}} {{interval}}\n📉 Major swing low broken\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_swing_choch and blalert.chochswing,
    title="🟢 Bullish CHoCH (Swing)",
    message="🟢 BULLISH CHANGE OF CHARACTER (Swing) detected on {{ticker}} {{interval}}\n🔄 Major trend shift to bullish\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_swing_choch and bralert.chochswing,
    title="🔴 Bearish CHoCH (Swing)",
    message="🔴 BEARISH CHANGE OF CHARACTER (Swing) detected on {{ticker}} {{interval}}\n🔄 Major trend shift to bearish\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_swing_chochplus and blalert.chochplusswing,
    title="🟢 Bullish CHoCH+ (Swing)",
    message="🟢 BULLISH ENHANCED CHANGE OF CHARACTER (Swing) detected on {{ticker}} {{interval}}\n🚀 Major bullish trend reversal\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_swing_chochplus and bralert.chochplusswing,
    title="🔴 Bearish CHoCH+ (Swing)",
    message="🔴 BEARISH ENHANCED CHANGE OF CHARACTER (Swing) detected on {{ticker}} {{interval}}\n💥 Major bearish trend reversal\n⏰ Time: {{time}}")

// Order Block Alerts
alertcondition(enable_alerts and alert_orderblocks and blalert.ob,
    title="🟢 Bullish Order Block",
    message="🟢 BULLISH ORDER BLOCK formed on {{ticker}} {{interval}}\n📦 Institutional buying zone identified\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_orderblocks and bralert.ob,
    title="🔴 Bearish Order Block",
    message="🔴 BEARISH ORDER BLOCK formed on {{ticker}} {{interval}}\n📦 Institutional selling zone identified\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_orderblocks and blalert.swingob,
    title="🟢 Bullish Swing Order Block",
    message="🟢 BULLISH SWING ORDER BLOCK formed on {{ticker}} {{interval}}\n📦 Major institutional buying zone\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_orderblocks and bralert.swingob,
    title="🔴 Bearish Swing Order Block",
    message="🔴 BEARISH SWING ORDER BLOCK formed on {{ticker}} {{interval}}\n📦 Major institutional selling zone\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_orderblock_touch and blalert.obtouch,
    title="🟢 Bullish Order Block Touch",
    message="🟢 BULLISH ORDER BLOCK TOUCHED on {{ticker}} {{interval}}\n🎯 Price reached institutional buying zone\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_orderblock_touch and bralert.obtouch,
    title="🔴 Bearish Order Block Touch",
    message="🔴 BEARISH ORDER BLOCK TOUCHED on {{ticker}} {{interval}}\n🎯 Price reached institutional selling zone\n⏰ Time: {{time}}")

// Fair Value Gap Alerts
alertcondition(enable_alerts and alert_fvg and blalert.fvg,
    title="🟢 Bullish Fair Value Gap",
    message="🟢 BULLISH FAIR VALUE GAP formed on {{ticker}} {{interval}}\n📊 Upward imbalance detected\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_fvg and bralert.fvg,
    title="🔴 Bearish Fair Value Gap",
    message="🔴 BEARISH FAIR VALUE GAP formed on {{ticker}} {{interval}}\n📊 Downward imbalance detected\n⏰ Time: {{time}}")

// Equal Highs/Lows Alerts
alertcondition(enable_alerts and alert_equal_levels and blalert.equal,
    title="🟡 Equal Highs/Lows (Bullish)",
    message="🟡 EQUAL HIGHS/LOWS detected on {{ticker}} {{interval}}\n⚖️ Liquidity zone identified (Bullish bias)\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_equal_levels and bralert.equal,
    title="🟡 Equal Highs/Lows (Bearish)",
    message="🟡 EQUAL HIGHS/LOWS detected on {{ticker}} {{interval}}\n⚖️ Liquidity zone identified (Bearish bias)\n⏰ Time: {{time}}")

// Accumulation/Distribution Zone Alerts
alertcondition(enable_alerts and alert_zones and blalert.zone,
    title="🟢 Accumulation Zone",
    message="🟢 ACCUMULATION ZONE detected on {{ticker}} {{interval}}\n📈 Smart money accumulating\n⏰ Time: {{time}}")

alertcondition(enable_alerts and alert_zones and bralert.zone,
    title="🔴 Distribution Zone",
    message="🔴 DISTRIBUTION ZONE detected on {{ticker}} {{interval}}\n📉 Smart money distributing\n⏰ Time: {{time}}")

// DEMA ATR Trend Alerts (Original alerts)
DemaAtrLong = close > ta.ema(close, 14)
DemaAtrShort = close < ta.ema(close, 14)

alertcondition(DemaAtrLong, title="📈 DEMA ATR Trend Up", message="📈 DEMA ATR TREND UP on {{ticker}} {{interval}}\n🚀 Bullish momentum confirmed\n⏰ Time: {{time}}")
alertcondition(DemaAtrShort, title="📉 DEMA ATR Trend Down", message="📉 DEMA ATR TREND DOWN on {{ticker}} {{interval}}\n💥 Bearish momentum confirmed\n⏰ Time: {{time}}")

// Reset alert flags after processing
if barstate.isconfirmed
    blalert.bos := false
    blalert.choch := false
    blalert.chochplus := false
    blalert.swingbos := false
    blalert.chochswing := false
    blalert.chochplusswing := false
    blalert.ob := false
    blalert.swingob := false
    blalert.obtouch := false
    blalert.fvg := false
    blalert.equal := false
    blalert.zone := false

    bralert.bos := false
    bralert.choch := false
    bralert.chochplus := false
    bralert.swingbos := false
    bralert.chochswing := false
    bralert.chochplusswing := false
    bralert.ob := false
    bralert.swingob := false
    bralert.obtouch := false
    bralert.fvg := false
    bralert.equal := false
    bralert.zone := false

//{----------------------------------------------------------------------------------------------------------------------------------------------}
//{ - ALERT DOCUMENTATION                                                                                                                        }
//{----------------------------------------------------------------------------------------------------------------------------------------------}
//
// ✅ COMPREHENSIVE ALERT SYSTEM IMPLEMENTED
//
// This enhanced version includes 22 different alert types:
//
// 📊 MARKET STRUCTURE ALERTS (12 types):
//    • Internal BOS (Bullish/Bearish)
//    • Internal CHoCH (Bullish/Bearish)
//    • Internal CHoCH+ (Bullish/Bearish)
//    • Swing BOS (Bullish/Bearish)
//    • Swing CHoCH (Bullish/Bearish)
//    • Swing CHoCH+ (Bullish/Bearish)
//
// 📦 ORDER BLOCK ALERTS (6 types):
//    • Bullish/Bearish Order Block Formation
//    • Bullish/Bearish Swing Order Block Formation
//    • Bullish/Bearish Order Block Touch/Mitigation
//
// 📊 PRICE ACTION ALERTS (4 types):
//    • Bullish/Bearish Fair Value Gap Formation
//    • Equal Highs/Lows Detection (Bullish/Bearish bias)
//
// 🏦 SMART MONEY ALERTS (2 types):
//    • Accumulation Zone Detection
//    • Distribution Zone Detection
//
// 📈 TREND ALERTS (2 types):
//    • DEMA ATR Trend Up
//    • DEMA ATR Trend Down
//
// 🎛️ ALERT CONTROLS:
//    • Master enable/disable switch
//    • Individual alert type toggles
//    • Rich message formatting with emojis
//    • Ticker and timeframe information
//    • Timestamp inclusion
//
// 🔧 USAGE:
//    1. Enable "Enable All Alerts" in Alert Settings
//    2. Toggle individual alert types as needed
//    3. Set up TradingView alerts using the alertcondition() functions
//    4. Alerts will fire when conditions are met and confirmed
//
//{----------------------------------------------------------------------------------------------------------------------------------------------}
